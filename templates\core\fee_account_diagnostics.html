{% extends 'base.html' %} {% load static %} {% load rbac_tags %}
{% load humanize %}

<!-- title -->
{% block title %}Fee Account Diagnostics | {% endblock %}
<!-- title -->

{% block content %}
<section class="w-full max-w-7xl mx-auto px-4 py-8 space-y-8">
  <!-- Messages Section -->
  {% if messages %}
  <div class="space-y-4">
    {% for message in messages %}
    <div
      class="card-modern p-4 border-l-4 {% if message.tags == 'success' %}border-[#74C69D] bg-[#74C69D]/10{% endif %} {% if message.tags == 'error' %}border-[#F28C8C] bg-[#F28C8C]/10{% endif %} {% if message.tags == 'warning' %}border-[#F59E0B] bg-[#F59E0B]/10{% endif %} {% if message.tags == 'info' %}border-[#7AB2D3] bg-[#7AB2D3]/10{% endif %} message-fade-in"
    >
      <div class="flex items-center gap-3">
        <i class="fas fa-info-circle text-lg"></i>
        <p class="font-medium">{{ message }}</p>
      </div>
    </div>
    {% endfor %}
  </div>
  {% endif %}

  <!-- Header Section -->
  <div
    class="card-modern p-6 sm:p-8 bg-gradient-to-r from-[#E2F1F9] to-[#B9D8EB] border-0 shadow-xl"
  >
    <div
      class="flex flex-col lg:flex-row lg:justify-between lg:items-center gap-6"
    >
      <!-- Title and Description -->
      <div class="flex items-center gap-4">
        <div
          class="w-12 h-12 sm:w-14 sm:h-14 bg-[#40657F] rounded-xl sm:rounded-2xl flex items-center justify-center shadow-xl icon-float"
        >
          <i
            class="fas fa-stethoscope text-white text-lg sm:text-xl icon-pulse"
          ></i>
        </div>
        <div>
          <h1
            class="font-display font-bold text-2xl sm:text-3xl md:text-4xl text-[#2C3E50] tracking-tight title-slide-in"
          >
            Fee Account Diagnostics
          </h1>
          <p
            class="text-[#40657F] text-base sm:text-lg font-medium mt-1 subtitle-fade-in"
          >
            Comprehensive system health analysis and issue detection
          </p>
          <div
            class="w-16 sm:w-20 h-1 bg-[#7AB2D3] rounded-full mt-2 accent-line-grow"
          ></div>
        </div>
      </div>

      <!-- Quick Actions -->
      <div class="flex flex-col sm:flex-row gap-3 action-buttons-slide-in">
        <a
          href="{% url 'core:administration' %}"
          class="bg-[#B9D8EB] text-[#40657F] font-semibold py-3 px-6 rounded-xl hover:bg-[#7AB2D3] hover:text-white focus:ring-4 focus:ring-[#B9D8EB]/30 transition-all duration-300 shadow-md hover:shadow-lg btn-modern group"
        >
          <i
            class="fas fa-arrow-left mr-2 group-hover:scale-110 transition-transform duration-300"
          ></i>
          Back to Administration
        </a>
        <a
          href="{% url 'core:comprehensive_fee_account_diagnostics' %}"
          class="bg-[#40657F] text-white font-semibold py-3 px-6 rounded-xl hover:bg-[#2C3E50] focus:ring-4 focus:ring-[#40657F]/30 transition-all duration-300 shadow-md hover:shadow-lg btn-modern group"
        >
          <i
            class="fas fa-sync-alt mr-2 group-hover:rotate-180 transition-transform duration-300"
          ></i>
          Refresh Diagnostics
        </a>
      </div>
    </div>
  </div>

  <!-- System Status Overview -->
  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
    <!-- Overall Status -->
    <div
      class="card-modern p-6 {% if diagnostics.summary.status == 'healthy' %}bg-gradient-to-br from-[#74C69D]/10 to-[#74C69D]/5 border-[#74C69D]/20{% elif diagnostics.summary.status == 'warning' %}bg-gradient-to-br from-[#F59E0B]/10 to-[#F59E0B]/5 border-[#F59E0B]/20{% else %}bg-gradient-to-br from-[#F28C8C]/10 to-[#F28C8C]/5 border-[#F28C8C]/20{% endif %}"
    >
      <div class="flex items-center gap-4">
        <div
          class="w-12 h-12 {% if diagnostics.summary.status == 'healthy' %}bg-[#74C69D]{% elif diagnostics.summary.status == 'warning' %}bg-[#F59E0B]{% else %}bg-[#F28C8C]{% endif %} rounded-xl flex items-center justify-center"
        >
          <i
            class="fas {% if diagnostics.summary.status == 'healthy' %}fa-check-circle{% elif diagnostics.summary.status == 'warning' %}fa-exclamation-triangle{% else %}fa-times-circle{% endif %} text-white text-lg"
          ></i>
        </div>
        <div>
          <h3 class="font-bold text-lg text-[#2C3E50]">System Status</h3>
          <p
            class="text-sm {% if diagnostics.summary.status == 'healthy' %}text-[#74C69D]{% elif diagnostics.summary.status == 'warning' %}text-[#F59E0B]{% else %}text-[#F28C8C]{% endif %} font-semibold capitalize"
          >
            {{ diagnostics.summary.status }}
          </p>
        </div>
      </div>
    </div>

    <!-- Total Issues -->
    <div
      class="card-modern p-6 bg-gradient-to-br from-[#7AB2D3]/10 to-[#7AB2D3]/5 border-[#7AB2D3]/20"
    >
      <div class="flex items-center gap-4">
        <div
          class="w-12 h-12 bg-[#7AB2D3] rounded-xl flex items-center justify-center"
        >
          <i class="fas fa-list-alt text-white text-lg"></i>
        </div>
        <div>
          <h3 class="font-bold text-lg text-[#2C3E50]">Total Issues</h3>
          <p class="text-2xl font-bold text-[#7AB2D3]">
            {{ diagnostics.summary.total_issues }}
          </p>
        </div>
      </div>
    </div>

    <!-- Critical Issues -->
    <div
      class="card-modern p-6 bg-gradient-to-br from-[#F28C8C]/10 to-[#F28C8C]/5 border-[#F28C8C]/20"
    >
      <div class="flex items-center gap-4">
        <div
          class="w-12 h-12 bg-[#F28C8C] rounded-xl flex items-center justify-center"
        >
          <i class="fas fa-exclamation-circle text-white text-lg"></i>
        </div>
        <div>
          <h3 class="font-bold text-lg text-[#2C3E50]">Critical Issues</h3>
          <p class="text-2xl font-bold text-[#F28C8C]">
            {{ diagnostics.summary.critical_issues }}
          </p>
        </div>
      </div>
    </div>

    <!-- Warnings -->
    <div
      class="card-modern p-6 bg-gradient-to-br from-[#F59E0B]/10 to-[#F59E0B]/5 border-[#F59E0B]/20"
    >
      <div class="flex items-center gap-4">
        <div
          class="w-12 h-12 bg-[#F59E0B] rounded-xl flex items-center justify-center"
        >
          <i class="fas fa-exclamation-triangle text-white text-lg"></i>
        </div>
        <div>
          <h3 class="font-bold text-lg text-[#2C3E50]">Warnings</h3>
          <p class="text-2xl font-bold text-[#F59E0B]">
            {{ diagnostics.summary.warnings }}
          </p>
        </div>
      </div>
    </div>
  </div>

  <!-- System Health Recommendations -->
  {% if diagnostics.system_health.recommendations %}
  <div
    class="card-modern p-6 bg-gradient-to-r from-[#E2F1F9] to-[#B9D8EB] border-0"
  >
    <div class="flex items-center gap-3 mb-4">
      <div
        class="w-10 h-10 bg-[#40657F] rounded-lg flex items-center justify-center"
      >
        <i class="fas fa-lightbulb text-white"></i>
      </div>
      <h2 class="font-bold text-xl text-[#2C3E50]">System Recommendations</h2>
    </div>
    <div class="space-y-3">
      {% for recommendation in diagnostics.system_health.recommendations %}
      <div class="flex items-start gap-3 p-3 bg-white/50 rounded-lg">
        <i class="fas fa-arrow-right text-[#7AB2D3] mt-1"></i>
        <p class="text-[#2C3E50] font-medium">{{ recommendation }}</p>
      </div>
      {% endfor %}
    </div>
  </div>
  {% endif %}

  <!-- Detailed Analysis Sections -->
  <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
    <!-- Terms Analysis -->
    <div class="card-modern p-6">
      <div class="flex items-center gap-3 mb-6">
        <div
          class="w-10 h-10 bg-[#40657F] rounded-lg flex items-center justify-center"
        >
          <i class="fas fa-calendar-alt text-white"></i>
        </div>
        <h2 class="font-bold text-xl text-[#2C3E50]">Terms Analysis</h2>
      </div>

      <!-- Active Terms -->
      <div class="mb-6">
        <h3 class="font-semibold text-lg text-[#40657F] mb-3">
          Active Terms ({{ diagnostics.terms.active_terms|length }})
        </h3>
        {% if diagnostics.terms.active_terms %}
        <div class="space-y-2">
          {% for term in diagnostics.terms.active_terms %}
          <div
            class="flex items-center justify-between p-3 bg-[#74C69D]/10 rounded-lg border border-[#74C69D]/20"
          >
            <div>
              <p class="font-medium text-[#2C3E50]">{{ term.term_name }}</p>
              <p class="text-sm text-[#40657F]">
                {{ term.academic_year.name }}
              </p>
            </div>
            <div class="text-right">
              <p class="text-sm text-[#74C69D] font-semibold">Active</p>
              <p class="text-xs text-[#40657F]">
                {{ term.start_date }} - {{ term.end_date }}
              </p>
            </div>
          </div>
          {% endfor %}
        </div>
        {% else %}
        <p class="text-[#F28C8C] font-medium">No active terms found</p>
        {% endif %}
      </div>

      <!-- Term Issues -->
      {% if diagnostics.terms.issues %}
      <div class="mb-4">
        <h3 class="font-semibold text-lg text-[#F28C8C] mb-3">Term Issues</h3>
        <div class="space-y-3">
          {% for issue in diagnostics.terms.issues %}
          <div
            class="p-4 {% if issue.type == 'critical' %}bg-[#F28C8C]/10 border-[#F28C8C]/20{% else %}bg-[#F59E0B]/10 border-[#F59E0B]/20{% endif %} rounded-lg border"
          >
            <div class="flex items-start gap-3">
              <i
                class="fas {% if issue.type == 'critical' %}fa-times-circle text-[#F28C8C]{% else %}fa-exclamation-triangle text-[#F59E0B]{% endif %} mt-1"
              ></i>
              <div class="flex-1">
                <p class="font-medium text-[#2C3E50] mb-1">
                  {{ issue.message }}
                </p>
                <p class="text-sm text-[#40657F] mb-2">{{ issue.solution }}</p>
                {% if issue.details %}
                <div class="text-xs text-[#40657F] space-y-1">
                  {% for detail in issue.details %}
                  <p>• {{ detail }}</p>
                  {% endfor %}
                </div>
                {% endif %}
              </div>
            </div>
          </div>
          {% endfor %}
        </div>
      </div>
      {% endif %}
    </div>

    <!-- Fee Categories Analysis -->
    <div class="card-modern p-6">
      <div class="flex items-center gap-3 mb-6">
        <div
          class="w-10 h-10 bg-[#40657F] rounded-lg flex items-center justify-center"
        >
          <i class="fas fa-tags text-white"></i>
        </div>
        <h2 class="font-bold text-xl text-[#2C3E50]">
          Fee Categories Analysis
        </h2>
      </div>

      <!-- Active Categories -->
      <div class="mb-6">
        <h3 class="font-semibold text-lg text-[#40657F] mb-3">
          Active Categories ({{diagnostics.fee_categories.active_categories|length }})
        </h3>
        {% if diagnostics.fee_categories.active_categories %}
        <div class="space-y-2">
          {% for category in diagnostics.fee_categories.active_categories %}
          <div
            class="flex items-center justify-between p-3 bg-[#74C69D]/10 rounded-lg border border-[#74C69D]/20"
          >
            <div>
              <p class="font-medium text-[#2C3E50]">{{ category.name }}</p>
            </div>
            <div class="text-right">
              <p class="text-sm text-[#74C69D] font-semibold">
                Mwk {{ category.amount|intcomma }}
              </p>
              <p class="text-xs text-[#40657F]">Active</p>
            </div>
          </div>
          {% endfor %}
        </div>
        {% else %}
        <p class="text-[#F28C8C] font-medium">No active fee categories found</p>
        {% endif %}
      </div>

      <!-- Category Issues -->
      {% if diagnostics.fee_categories.issues %}
      <div class="mb-4">
        <h3 class="font-semibold text-lg text-[#F28C8C] mb-3">
          Category Issues
        </h3>
        <div class="space-y-3">
          {% for issue in diagnostics.fee_categories.issues %}
          <div
            class="p-4 {% if issue.type == 'critical' %}bg-[#F28C8C]/10 border-[#F28C8C]/20{% else %}bg-[#F59E0B]/10 border-[#F59E0B]/20{% endif %} rounded-lg border"
          >
            <div class="flex items-start gap-3">
              <i
                class="fas {% if issue.type == 'critical' %}fa-times-circle text-[#F28C8C]{% else %}fa-exclamation-triangle text-[#F59E0B]{% endif %} mt-1"
              ></i>
              <div class="flex-1">
                <p class="font-medium text-[#2C3E50] mb-1">
                  {{ issue.message }}
                </p>
                <p class="text-sm text-[#40657F]">{{ issue.solution }}</p>
              </div>
            </div>
          </div>
          {% endfor %}
        </div>
      </div>
      {% endif %}
    </div>
  </div>

  <!-- Students Analysis -->
  <div class="card-modern p-6">
    <div class="flex items-center gap-3 mb-6">
      <div
        class="w-10 h-10 bg-[#40657F] rounded-lg flex items-center justify-center"
      >
        <i class="fas fa-users text-white"></i>
      </div>
      <h2 class="font-bold text-xl text-[#2C3E50]">Students Analysis</h2>
    </div>

    <!-- Student Statistics -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
      <div class="p-4 bg-[#74C69D]/10 rounded-lg border border-[#74C69D]/20">
        <div class="flex items-center gap-3">
          <i class="fas fa-user-check text-[#74C69D] text-xl"></i>
          <div>
            <p class="text-sm text-[#40657F] font-medium">Active Students</p>
            <p class="text-2xl font-bold text-[#74C69D]">
              {{ diagnostics.students.active_students_count }}
            </p>
          </div>
        </div>
      </div>
      <div class="p-4 bg-[#B9D8EB]/20 rounded-lg border border-[#B9D8EB]/30">
        <div class="flex items-center gap-3">
          <i class="fas fa-user-times text-[#40657F] text-xl"></i>
          <div>
            <p class="text-sm text-[#40657F] font-medium">Inactive Students</p>
            <p class="text-2xl font-bold text-[#40657F]">
              {{ diagnostics.students.inactive_students_count }}
            </p>
          </div>
        </div>
      </div>
    </div>

    <!-- Students Without Accounts -->
    {% if diagnostics.students.students_without_accounts %}
    <div class="mb-6">
      <h3 class="font-semibold text-lg text-[#F59E0B] mb-3">
        Students Without Fee Accounts 
        ({{ diagnostics.students.students_without_accounts|length }})
      </h3>
      <div class="max-h-64 overflow-y-auto space-y-2">
        {% for student_data in diagnostics.students.students_without_accounts %}
        <div
          class="flex items-center justify-between p-3 bg-[#F59E0B]/10 rounded-lg border border-[#F59E0B]/20"
        >
          <div>
            <p class="font-medium text-[#2C3E50]">
              {{ student_data.student.name }}
            </p>
            <p class="text-sm text-[#40657F]">{{ student_data.level }} ({{ student_data.education_stage }})</p>
          </div>
          <div class="text-right">
            <p class="text-sm text-[#F59E0B] font-semibold">No Accounts</p>
            <p class="text-xs text-[#40657F]">
              ID: {{ student_data.student_id }}
            </p>
          </div>
        </div>
        {% endfor %}
      </div>
    </div>
    {% endif %}

    <!-- Students With Partial Accounts -->
    {% if diagnostics.students.students_with_partial_accounts %}
    <div class="mb-6">
      <h3 class="font-semibold text-lg text-[#F59E0B] mb-3">
        Students With Incomplete Accounts 
        ({{ diagnostics.students.students_with_partial_accounts|length }})
      </h3>
      <div class="max-h-64 overflow-y-auto space-y-2">
        {% for student_data in diagnostics.students.students_with_partial_accounts %}
        <div class="p-3 bg-[#F59E0B]/10 rounded-lg border border-[#F59E0B]/20">
          <div class="flex items-center justify-between mb-2">
            <div>
              <p class="font-medium text-[#2C3E50]">
                {{ student_data.student.name }}
              </p>
              <p class="text-sm text-[#40657F]">{{ student_data.level }} ({{ student_data.education_stage }})</p>
            </div>
            <div class="text-right">
              <p class="text-sm text-[#F59E0B] font-semibold">
                {{ student_data.existing_accounts }}/
                {{ student_data.expected_accounts }} Accounts
              </p>
              <p class="text-xs text-[#40657F]">
                ID: {{ student_data.student_id }}
              </p>
            </div>
          </div>
          <div class="text-xs text-[#40657F]">
            <p class="font-medium mb-1">Missing Categories:</p>
            <div class="flex flex-wrap gap-1">
              {% for category in student_data.missing_categories %}
              <span class="px-2 py-1 bg-[#F59E0B]/20 rounded text-[#F59E0B]"
                >{{ category.name }}</span
              >
              {% endfor %}
            </div>
          </div>
        </div>
        {% endfor %}
      </div>
    </div>
    {% endif %}

    <!-- Student Issues -->
    {% if diagnostics.students.issues %}
    <div class="mb-4">
      <h3 class="font-semibold text-lg text-[#F28C8C] mb-3">Student Issues</h3>
      <div class="space-y-3">
        {% for issue in diagnostics.students.issues %}
        <div
          class="p-4 {% if issue.type == 'critical' %}bg-[#F28C8C]/10 border-[#F28C8C]/20{% else %}bg-[#F59E0B]/10 border-[#F59E0B]/20{% endif %} rounded-lg border"
        >
          <div class="flex items-start gap-3">
            <i
              class="fas {% if issue.type == 'critical' %}fa-times-circle text-[#F28C8C]{% else %}fa-exclamation-triangle text-[#F59E0B]{% endif %} mt-1"
            ></i>
            <div class="flex-1">
              <p class="font-medium text-[#2C3E50] mb-1">{{ issue.message }}</p>
              <p class="text-sm text-[#40657F]">{{ issue.solution }}</p>
            </div>
          </div>
        </div>
        {% endfor %}
      </div>
    </div>
    {% endif %}
  </div>

  <!-- Fee Accounts Analysis -->
  <div class="card-modern p-6">
    <div class="flex items-center gap-3 mb-6">
      <div
        class="w-10 h-10 bg-[#40657F] rounded-lg flex items-center justify-center"
      >
        <i class="fas fa-file-invoice-dollar text-white"></i>
      </div>
      <h2 class="font-bold text-xl text-[#2C3E50]">Fee Accounts Analysis</h2>
    </div>

    <!-- Account Statistics -->
    <div class="p-4 bg-[#7AB2D3]/10 rounded-lg border border-[#7AB2D3]/20 mb-6">
      <div class="flex items-center gap-3">
        <i class="fas fa-calculator text-[#7AB2D3] text-xl"></i>
        <div>
          <p class="text-sm text-[#40657F] font-medium">
            Total Active Fee Accounts
          </p>
          <p class="text-2xl font-bold text-[#7AB2D3]">
            {{ diagnostics.fee_accounts.total_accounts }}
          </p>
        </div>
      </div>
    </div>

    <!-- Orphaned Accounts -->
    {% if diagnostics.fee_accounts.orphaned_accounts %}
    <div class="mb-6">
      <h3 class="font-semibold text-lg text-[#F59E0B] mb-3">
        Orphaned Accounts ({{ diagnostics.fee_accounts.orphaned_accounts|length
        }})
      </h3>
      <div class="max-h-64 overflow-y-auto space-y-2">
        {% for account_data in diagnostics.fee_accounts.orphaned_accounts %}
        <div
          class="flex items-center justify-between p-3 bg-[#F59E0B]/10 rounded-lg border border-[#F59E0B]/20"
        >
          <div>
            <p class="font-medium text-[#2C3E50]">
              {{ account_data.student_name }}
            </p>
            <p class="text-sm text-[#40657F]">{{ account_data.category }}</p>
          </div>
          <div class="text-right">
            <p class="text-sm text-[#F59E0B] font-semibold">
              Mwk {{ account_data.total_due|floatformat:0 }}
            </p>
            <p class="text-xs text-[#40657F]">
              {{ account_data.billing_cycle }}
            </p>
          </div>
        </div>
        {% endfor %}
      </div>
    </div>
    {% endif %}

    <!-- Zero Due Accounts -->
    {% if diagnostics.fee_accounts.accounts_with_zero_due %}
    <div class="mb-6">
      <h3 class="font-semibold text-lg text-[#F59E0B] mb-3">
        Accounts With Zero Due ({{
        diagnostics.fee_accounts.accounts_with_zero_due|length }})
      </h3>
      <div class="max-h-64 overflow-y-auto space-y-2">
        {% for account_data in diagnostics.fee_accounts.accounts_with_zero_due %}
        <div
          class="flex items-center justify-between p-3 bg-[#F59E0B]/10 rounded-lg border border-[#F59E0B]/20"
        >
          <div>
            <p class="font-medium text-[#2C3E50]">
              {{ account_data.student_name }}
            </p>
            <p class="text-sm text-[#40657F]">{{ account_data.category }}</p>
          </div>
          <div class="text-right">
            <p class="text-sm text-[#F59E0B] font-semibold">Mwk 0</p>
            <p class="text-xs text-[#40657F]">
              {{ account_data.billing_cycle }}
            </p>
          </div>
        </div>
        {% endfor %}
      </div>
    </div>
    {% endif %}

    <!-- Fee Account Issues -->
    {% if diagnostics.fee_accounts.issues %}
    <div class="mb-4">
      <h3 class="font-semibold text-lg text-[#F28C8C] mb-3">
        Fee Account Issues
      </h3>
      <div class="space-y-3">
        {% for issue in diagnostics.fee_accounts.issues %}
        <div
          class="p-4 {% if issue.type == 'critical' %}bg-[#F28C8C]/10 border-[#F28C8C]/20{% else %}bg-[#F59E0B]/10 border-[#F59E0B]/20{% endif %} rounded-lg border"
        >
          <div class="flex items-start gap-3">
            <i
              class="fas {% if issue.type == 'critical' %}fa-times-circle text-[#F28C8C]{% else %}fa-exclamation-triangle text-[#F59E0B]{% endif %} mt-1"
            ></i>
            <div class="flex-1">
              <p class="font-medium text-[#2C3E50] mb-1">{{ issue.message }}</p>
              <p class="text-sm text-[#40657F]">{{ issue.solution }}</p>
            </div>
          </div>
        </div>
        {% endfor %}
      </div>
    </div>
    {% endif %}
  </div>

  <!-- Quick Actions Section -->
  <div
    class="card-modern p-6 bg-gradient-to-r from-[#E2F1F9] to-[#B9D8EB] border-0"
  >
    <div class="flex items-center gap-3 mb-6">
      <div
        class="w-10 h-10 bg-[#40657F] rounded-lg flex items-center justify-center"
      >
        <i class="fas fa-tools text-white"></i>
      </div>
      <h2 class="font-bold text-xl text-[#2C3E50]">Quick Actions</h2>
    </div>
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      <a
        href="{% url 'core:run_create_student_accounts' %}"
        class="flex items-center gap-3 p-4 bg-white/70 rounded-lg hover:bg-white/90 transition-all duration-300 shadow-md hover:shadow-lg group"
      >
        <div
          class="w-10 h-10 bg-[#74C69D] rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-300"
        >
          <i class="fas fa-plus text-white"></i>
        </div>
        <div>
          <p class="font-semibold text-[#2C3E50]">Create Accounts</p>
          <p class="text-sm text-[#40657F]">Generate missing fee accounts</p>
        </div>
      </a>
      <a
        href="{% url 'core:run_recalculate_accounts' %}"
        class="flex items-center gap-3 p-4 bg-white/70 rounded-lg hover:bg-white/90 transition-all duration-300 shadow-md hover:shadow-lg group"
      >
        <div
          class="w-10 h-10 bg-[#7AB2D3] rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-300"
        >
          <i class="fas fa-calculator text-white"></i>
        </div>
        <div>
          <p class="font-semibold text-[#2C3E50]">Recalculate Accounts</p>
          <p class="text-sm text-[#40657F]">Clean up and recalculate</p>
        </div>
      </a>
      <a
        href="{% url 'core:diagnose_fee_accounts' %}"
        class="flex items-center gap-3 p-4 bg-white/70 rounded-lg hover:bg-white/90 transition-all duration-300 shadow-md hover:shadow-lg group"
      >
        <div
          class="w-10 h-10 bg-[#F59E0B] rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-300"
        >
          <i class="fas fa-stethoscope text-white"></i>
        </div>
        <div>
          <p class="font-semibold text-[#2C3E50]">Quick Diagnostics</p>
          <p class="text-sm text-[#40657F]">Run basic diagnostics</p>
        </div>
      </a>
    </div>
  </div>
</section>
{% endblock %}

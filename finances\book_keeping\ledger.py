from datetime import date
from django.db import models
from django.utils.text import slugify
from django.contrib.auth import get_user_model

from helpers.strings import generate_unique_ids

User = get_user_model()


class Ledger(models.Model):
    '''
        Account is a record of all the transactions that have been made in the system. It should have a unique slug that is generated when the entry is created.
    '''
    code = models.CharField(max_length=100, blank=True, null=True, unique=True)
    name = models.CharField(max_length=100)
    ledger_type = models.CharField(max_length=50, choices=[
        ('Asset', 'Asset'),
        ('Liability', 'Liability'),
        ('Equity', 'Equity'),
        ('Revenue', 'Revenue'),
        ('Expense', 'Expense'),
    ])

    slug = models.SlugField(max_length=200, unique=True, blank=True, null=True)

    def date_range_total(self, start_date, end_date):
        return sum(
            line.amount for line in self.journal_line.filter(
                journal_entry__is_reversal=False,  # Only non-reversal entries
                journal_entry__is_locked=False,    # Exclude locked (reversed) entries
                journal_entry__date__range=(start_date, end_date)
            )
        ) or 0

    @property
    def total_amount(self):
        from students.models import Term
        term = Term.objects.get_active()

        return sum(
            line.amount for line in self.journal_line.filter(
                journal_entry__is_reversal=False,  # Only non-reversal entries
                journal_entry__is_locked=False,    # Exclude locked (reversed) entries
                journal_entry__term=term
            )
        ) or 0

    def save(self, *args, **kwargs):
        super().save(*args, **kwargs)

        if not self.slug:
            self.slug = slugify(f"{self.code}")
            super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.code} | {self.name} | {self.ledger_type}"


class JournalEntry(models.Model):
    '''
        Journal entry is a record of all the transactions that have been made in the system. It should have a unique slug that is generated when the entry is created.
    '''
    journal_number = models.CharField(max_length=100, blank=True, null=True)
    date = models.DateField(default=date.today)
    voucher = models.CharField(max_length=100, blank=True, null=True)
    term = models.ForeignKey('students.Term', on_delete=models.CASCADE)
    description = models.TextField(blank=True, null=True)
    slug = models.SlugField(max_length=200, unique=True, blank=True, null=True)

    # For reversing journal entries
    reverses = models.OneToOneField(
        'self', null=True, blank=True, on_delete=models.SET_NULL, related_name="reversal_of"
    )
    is_reversal = models.BooleanField(default=False)
    created_by = models.ForeignKey(User, null=True, on_delete=models.SET_NULL)
    created_at = models.DateTimeField(auto_now_add=True)
    reason = models.TextField(blank=True, null=True)
    is_locked = models.BooleanField(default=False)

    def generate_journal_number(self):
        prefix = "JRN"
        self.journal_number = generate_unique_ids(self, prefix)

    def is_balanced(self):
        lines = self.journalline_set.all()
        debit_total = sum(
            line.amount for line in lines if line.line_type == 'Debit')
        credit_total = sum(
            line.amount for line in lines if line.line_type == 'Credit')

        return debit_total == credit_total

    def save(self, *args, **kwargs):
        super().save(*args, **kwargs)

        if not self.journal_number:
            self.generate_journal_number()
            super().save(update_fields=["journal_number", "slug"])

    def __str__(self):
        return f"{self.journal_number} - {self.term} - {self.date}"

    class Meta:
        unique_together = ('journal_number', 'voucher')
        ordering = ['-date']


class JournalLine(models.Model):
    '''
        Journal entry item is a record of all the transactions that have been made in the system. It should have a unique slug that is generated when the entry is created.
    '''
    journal_entry = models.ForeignKey(JournalEntry, on_delete=models.CASCADE)
    account = models.ForeignKey(
        Ledger, on_delete=models.CASCADE, related_name="journal_line")
    description = models.TextField(blank=True, null=True)
    amount = models.DecimalField(default=0, decimal_places=2, max_digits=20)
    line_type = models.CharField(max_length=50, choices=[
        ('Debit', 'Debit'),
        ('Credit', 'Credit'),
    ])

    def save(self, *args, **kwargs):
        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.journal_entry} - {self.line_type} - MWk {self.amount}"

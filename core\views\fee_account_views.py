from django.shortcuts import redirect, render
from django.contrib import messages

from core.services import create_new_fee_accounts, re_calculate_accounts, rearrange_receipts_by_month
from core.services.fee_accounts import allign_journals, diagnose_fee_account_issues
from students.utils import term_utils
from accounts.decorators import require_permission, admin_required


@require_permission('manage_system')
def run_create_student_accounts(request):
    """Create new fee accounts for all active students - requires manage_system permission"""
    try:
        create_new_fee_accounts()
        messages.success(
            request, "Successfully created new fee accounts for all active students.")
    except Exception as e:
        messages.error(request, f"Error creating fee accounts: {str(e)}")
    return redirect('core:administration')


@require_permission('manage_system')
def run_recalculate_accounts(request):
    """Recalculate all fee accounts - requires manage_system permission"""
    try:
        re_calculate_accounts()
        messages.success(
            request, "Successfully recalculated all fee accounts.")
    except Exception as e:
        messages.error(request, f"Error recalculating accounts: {str(e)}")
    return redirect('core:administration')


@require_permission('manage_system')
def rearrange_receipts(request):
    """Rearrange receipts by month - requires manage_system permission"""
    try:
        rearrange_receipts_by_month()
        messages.success(request, "Successfully rearranged receipts by month.")
    except Exception as e:
        messages.error(request, f"Error rearranging receipts: {str(e)}")
    return redirect('core:administration')


@require_permission('manage_academic_periods')
def start_new_term(request):
    """Start a new academic term - requires manage_academic_periods permission"""
    try:
        term_utils.activate_term()
        is_done = term_utils.activate_term()
        if is_done:
            create_new_fee_accounts()
        messages.success(
            request, "Successfully started new term and created fee accounts.")
    except Exception as e:
        messages.error(request, f"Error starting new term: {str(e)}")
    return redirect('core:administration')


@require_permission('manage_system')
def allign_receipts_journals(request):
    """Align receipts with journal entries - requires manage_system permission"""
    try:
        allign_journals()
        messages.success(
            request, "Successfully aligned receipts with journal entries.")
    except Exception as e:
        messages.error(
            request, f"Error aligning receipts and journals: {str(e)}")
    return redirect('core:administration')


@require_permission('manage_system')
def diagnose_fee_accounts(request):
    """Diagnose fee account issues - requires manage_system permission"""
    try:
        issues = diagnose_fee_account_issues()
        if issues:
            for issue in issues:
                messages.warning(request, issue)
            messages.info(
                request, f"Found {len(issues)} potential issues with fee accounts.")
        else:
            messages.success(
                request, "No fee account issues detected. System is healthy.")
    except Exception as e:
        messages.error(
            request, f"Error running fee account diagnostics: {str(e)}")
    return redirect('core:administration')

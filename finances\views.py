from django.shortcuts import redirect, render, get_object_or_404
from django.contrib import messages
from django.contrib.auth.decorators import login_required
from django.db.models import Sum, Q
from django.core.paginator import Paginator

from finances.forms.outflow_forms import OutFlowLineFormSet, OutflowForm, ReversalReasonForm
from students.utils.fee_calculations import re_work_on_expenditurers

from finances.book_keeping import JournalLine, Outflow, JournalEntry
from finances.fee_management.models.receipt import Receipt
from core.services import journal_reversals




@login_required(login_url="accounts:login")
def expenditure_list(request):
    total_collected = JournalLine.objects.filter(
        journal_entry__term__is_active=True, account__ledger_type="Revenue", journal_entry__is_reversal=False).aggregate(total=Sum('amount'))['total'] or 0
    total_used = JournalLine.objects.filter(
        journal_entry__term__is_active=True, line_type="Debit", journal_entry__is_reversal=False).exclude(account__code="1001").aggregate(total=Sum('amount'))['total'] or 0
    total_balance = total_collected - total_used

    expenditure_list = Outflow.objects.filter(
        term__is_active=True, is_reversed=False).order_by('-date') or []

    context = {
        'expenditure_list': expenditure_list,
        'total_collected': total_collected,
        'total_used': total_used,
        'total_balance': total_balance,
    }
    return render(request, 'finances/expenditures.html', context)


@login_required(login_url="accounts:login")
def expenditure_detail(request, slug):
    try:
        expenditure = Outflow.objects.get(
            slug=slug, term__is_active=True)
    except Outflow.DoesNotExist:
        messages.error(request, "Expenditure not found.")
        return redirect('finances:expenditures')

    context = {
        'expenditure': expenditure,
    }
    return render(request, 'finances/expenditure_detail.html', context)


@login_required(login_url="accounts:login")
def add_expenditure(request):
    from students.models import Term

    term = Term.objects.get_active()
    form = OutflowForm()
    formset = OutFlowLineFormSet()


    if request.method == 'POST':
        form = OutflowForm(request.POST)
        formset = OutFlowLineFormSet(request.POST)

        if form.is_valid() and formset.is_valid():
            outflow = form.save(commit=False)
            outflow.created_by = request.user
            outflow.term = term
            outflow.save()

            lines = formset.save(commit=False)
            for line in lines:
                line.outflow = outflow
                line.save()

            outflow.update_journal()

            return redirect('finances:expenditures')

    context = {
        'form': form,
        'formset': formset,
    }

    return render(request, 'finances/add_expenditure.html', context)


@login_required(login_url="accounts:login")
def recalculate_expenditure(request):
    re_work_on_expenditurers()
    return redirect('students:administration')


# Reversal Views
@login_required(login_url="accounts:login")
def reverse_outflow(request, slug):
    """Reverse an outflow transaction"""
    outflow = get_object_or_404(Outflow, slug=slug, is_reversed=False)

    if request.method == 'POST':
        print("POST request received for outflow")
        form = ReversalReasonForm(request.POST)
        print(f"Form data: {request.POST}")
        if form.is_valid():
            print("Form is valid")
            reason = form.cleaned_data['reason']
            print(f"Reason: {reason}")
            try:
                print("About to call reverse_transaction for outflow")
                reversal_entry = journal_reversals.reverse_transaction(
                    outflow, reason, request.user
                )
                print(f"Reversal entry created: {reversal_entry}")
                messages.success(
                    request,
                    f'Outflow {outflow.outflow_id} has been successfully reversed. '
                    f'Reversal entry: {reversal_entry.voucher}'
                )
                return redirect('finances:expenditure_detail', slug=outflow.slug)
            except Exception as e:
                print(f"Exception occurred: {str(e)}")
                messages.error(request, f'Failed to reverse outflow: {str(e)}')
        else:
            print(f"Form is not valid. Errors: {form.errors}")
    else:
        form = ReversalReasonForm()

    context = {
        'form': form,
        'outflow': outflow,
        'title': f'Reverse Outflow {outflow.outflow_id}',
    }
    return render(request, 'finances/reverse_outflow.html', context)


@login_required(login_url="accounts:login")
def reverse_receipt(request, slug):
    """Reverse a receipt transaction"""
    receipt = get_object_or_404(Receipt, slug=slug, is_reversed=False)

    if request.method == 'POST':
        print("POST request received")
        form = ReversalReasonForm(request.POST)
        print(f"Form data: {request.POST}")
        if form.is_valid():
            print("Form is valid")
            reason = form.cleaned_data['reason']
            print(f"Reason: {reason}")
            try:
                print("About to call reverse_transaction")
                reversal_entry = journal_reversals.reverse_transaction(
                    receipt, reason, request.user
                )
                print(f"Reversal entry created: {reversal_entry}")
                messages.success(
                    request,
                    f'Receipt {receipt.receipt_number} has been successfully reversed. '
                    f'Reversal entry: {reversal_entry.voucher}'
                )
                return redirect('students:student_details', slug=receipt.student.student_id)
            except Exception as e:
                print(f"Exception occurred: {str(e)}")
                messages.error(request, f'Failed to reverse receipt: {str(e)}')
        else:
            print(f"Form is not valid. Errors: {form.errors}")
    else:
        form = ReversalReasonForm()

    context = {
        'form': form,
        'receipt': receipt,
        'title': f'Reverse Receipt {receipt.receipt_number}',
    }
    return render(request, 'finances/reverse_receipt.html', context)


@login_required(login_url="accounts:login")
def reversal_list(request):
    """List all reversed transactions"""
    search_query = request.GET.get('search', '')

    # Get all reversal journal entries
    reversals = JournalEntry.objects.filter(is_reversal=True).order_by('-date')

    if search_query:
        reversals = reversals.filter(
            Q(voucher__icontains=search_query) |
            Q(reason__icontains=search_query) |
            Q(created_by__username__icontains=search_query)
        )

    paginator = Paginator(reversals, 25)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'page_obj': page_obj,
        'search_query': search_query,
        'total_count': reversals.count(),
    }
    return render(request, 'finances/reversal_list.html', context)


@login_required(login_url="accounts:login")
def reversal_detail(request, voucher):
    """View details of a specific reversal"""
    reversal = get_object_or_404(
        JournalEntry, voucher=voucher, is_reversal=True)

    # Get the original transaction that was reversed
    original_transaction = reversal.reverses

    context = {
        'reversal': reversal,
        'original_transaction': original_transaction,
    }
    return render(request, 'finances/reversal_detail.html', context)

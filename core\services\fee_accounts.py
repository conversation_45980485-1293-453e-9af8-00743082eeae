from datetime import date
import logging

from finances.book_keeping import JournalEntry, JournalLine, Ledger
from finances.fee_management.models import FeeAccount, Receipt
from django.db import transaction
from django.db.models import Sum

from students.models import Student
from dateutil.relativedelta import relativedelta


# Configer logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Get the current term and month
current_month = date.today().replace(day=1)
previous_month = (current_month - relativedelta(months=1)).replace(day=1)


def create_new_fee_accounts():
    logger.info("Running create_new_fee_accounts() function")
    students = Student.objects.filter(is_active=True)

    success_count = 0
    error_count = 0

    for student in students:
        try:
            result = student.create_fee_accounts()
            if result:
                success_count += 1
                logger.info(
                    f"Successfully processed fee accounts for student: {student.name}")
            else:
                error_count += 1
                logger.warning(
                    f"Failed to create fee accounts for student: {student.name}")
        except Exception as e:
            error_count += 1
            logger.error(f"Error processing student {student.name}: {str(e)}")

    logger.info(
        f"Completed create_new_fee_account() function. Success: {success_count}, Errors: {error_count}")


def re_calculate_accounts():
    logger.info("Running re_calculate_accounts function")
    fee_accounts = FeeAccount.objects.filter(term__is_active=True)

    for account in fee_accounts:
        receipts = Receipt.objects.filter(
            fee_account=account, is_reversed=False)
        if not receipts:
            account.amount_paid = 0
            account.save()
            continue

        amount_paid = sum(receipt.amount_paid for receipt in receipts)
        try:
            with transaction.atomic():
                new_account = get_new_account(account, receipts.first())

                account.amount_paid = 0
                account.save()

                new_account.amount_paid = amount_paid
                new_account.save()

                logger.info(
                    f"Recalculated account for student {new_account.student} for month {new_account.month or "Termly"}")

        except FeeAccount.DoesNotExist:
            logger.error(
                f"FeeAccount does not exist for student {new_account.student} and month {new_account.month or 'Termly'}")
        except Exception as e:
            logger.error(
                f"Error recalculating account for student {account.student}: {e}")

    logger.info("Completed re_calculate_accounts function")


def get_student_account(student, month):
    """
    Helper function to get student's fee account for a student for a specific given month.
    """

    try:
        return FeeAccount.objects.get(student=student, month__month=month.month, term__is_active=True)
    except FeeAccount.DoesNotExist:
        logger.warning(
            f"Fee account for student {student} and month {month} does not exist.")
        return None


def get_new_account(account, receipt):
    if account.month:
        return FeeAccount.objects.get(
            term__is_active=True, month=receipt.date.replace(day=1), student=account.student
        )
    else:
        return account


def rearrange_receipts_by_month():
    '''
    In cases where the receipts had a different month to the fee_account
    this function will get those receipts and re_arrange them accordingly
    to reflect the date and actual fee account for that date(month)
    '''
    logger.info("Running rearrange_receipts_by_month function")
    receipts = Receipt.objects.filter(
        fee_account__term__is_active=True, is_reversed=False)

    for receipt in receipts:
        try:
            if receipt.fee_account.month is None:

                logger.info(
                    f"Skipping receipt {receipt.receipt_number} as fee account month is None")
                continue

            if receipt.date.month == receipt.fee_account.month.month:

                logger.info(
                    f"Skipping receipt {receipt.receipt_number} as it is already in the correct month")
                continue

            with transaction.atomic():
                # Get the correct fee account baseed on the receipt date
                correct_month = receipt.date.replace(day=1)
                correct_fee_account = FeeAccount.objects.get(
                    student=receipt.fee_account.student,
                    month=correct_month,
                    term__is_active=True,
                )

                receipt.fee_account = correct_fee_account
                receipt.save()

                logger.info(
                    f"Reassigned receipt {receipt.receipt_number} to correct month {correct_month}")

        except FeeAccount.DoesNotExist:
            logger.error(
                f"FeeAccount does not exist for student {receipt.fee_account.student} and month {correct_month}")
        except Exception as e:
            logger.error(
                f"Error reassigning receipt {receipt.receipt_number}: {e}")

    logger.info("Completed rearrange_receipts_by_month function")


def allign_journals():
    """
    Syncs journals with receipts using double-entry accounting.
    - Deletes journals without matching receipts.
    - Creates missing journals from receipts.
    - Updates mismatched amounts.
    """
    logger.info("Starting allign_journals function")

    receipts = Receipt.objects.filter(
        fee_account__term__is_active=True, is_reversed=False).select_related('fee_account__term')
    receipt_map = {r.receipt_number: r for r in receipts}

    journals = JournalLine.objects.filter(
        journal_entry__term__is_active=True,
        account__ledger_type="Revenue",
        line_type="Credit"
    ).select_related('journal_entry', 'account')

    journal_map = {j.journal_entry.voucher: j for j in journals}

    # STEP 1: Delete orphaned journals
    for voucher, journal in journal_map.items():
        if voucher not in receipt_map:
            journal.journal_entry.delete()  # Deletes both header and lines
            logger.info(
                f"Deleted orphaned journal {voucher} (no matching receipt)")

    # STEP 2: Create or update journals for receipts
    for receipt in receipts:
        voucher = receipt.receipt_number
        journal = journal_map.get(voucher)

        if journal:
            # Update if amount is off
            if journal.amount != receipt.amount_paid:
                journal.amount = receipt.amount_paid
                journal.save()
                logger.info(
                    f"Updated journal {voucher} amount to match receipt")
            continue

        revenue_account = Ledger.objects.get(
            name=receipt.fee_account.category.name)
        cash_account = Ledger.objects.get(name="Cash/Bank")

        entry = JournalEntry.objects.create(
            voucher=voucher,
            description=f"Receipt {voucher}",
            term=receipt.fee_account.term
        )

        JournalLine.objects.bulk_create([
            JournalLine(
                journal_entry=entry,
                account=cash_account,
                line_type="Debit",
                amount=receipt.amount_paid
            ),
            JournalLine(
                journal_entry=entry,
                account=revenue_account,
                line_type="Credit",
                amount=receipt.amount_paid
            )
        ])

        logger.info(f"Created journal {voucher} from receipt")

    # STEP 3: Verify total alignment
    total_receipts = receipts.aggregate(total=Sum("amount_paid"))['total'] or 0
    total_journals = JournalLine.objects.filter(
        journal_entry__term__is_active=True,
        account__ledger_type="Revenue",
        line_type="Credit"
    ).aggregate(total=Sum("amount"))['total'] or 0

    logger.info(f"Total receipt amount: {total_receipts}")
    logger.info(f"Total credited revenue: {total_journals}")

    if total_receipts != total_journals:
        logger.warning("Mismatch between receipts and revenue journals!")
    else:
        logger.info("Receipts and journals are perfectly aligned.")

    logger.info("Completed allign_journals function")


def diagnose_fee_account_issues():
    """
    Diagnostic function to identify common issues with fee account creation
    """
    logger.info("Running fee account diagnostics...")

    issues = []

    # Check for active terms
    from students.models import Term
    active_terms = Term.objects.filter(is_active=True)
    if not active_terms.exists():
        issues.append("No active term found - fee accounts cannot be created")
    elif active_terms.count() > 1:
        issues.append(
            f"Multiple active terms found ({active_terms.count()}) - this may cause conflicts")

    # Check for active fee categories
    from finances.fee_management.models import FeeCategory
    active_categories = FeeCategory.objects.filter(is_active=True)
    if not active_categories.exists():
        issues.append(
            "No active fee categories found - no accounts will be created")

    # Check for students without fee accounts
    from students.models import Student
    active_students = Student.objects.filter(is_active=True)
    students_without_accounts = []

    for student in active_students:
        accounts = FeeAccount.objects.filter(
            student=student, term__is_active=True)
        if not accounts.exists():
            students_without_accounts.append(student.name)

    if students_without_accounts:
        issues.append(f"Students without fee accounts: {', '.join(students_without_accounts[:5])}" +
                      (f" and {len(students_without_accounts) - 5} more" if len(students_without_accounts) > 5 else ""))

    # Check for orphaned fee accounts
    orphaned_accounts = FeeAccount.objects.filter(
        student__is_active=False, term__is_active=True)
    if orphaned_accounts.exists():
        issues.append(
            f"Found {orphaned_accounts.count()} fee accounts for inactive students")

    # Log results
    if issues:
        logger.warning("Fee account issues found:")
        for issue in issues:
            logger.warning(f"  - {issue}")
    else:
        logger.info("No fee account issues detected")

    return issues


def get_comprehensive_fee_account_diagnostics():
    """
    Comprehensive diagnostic function that returns detailed data structures
    for the dedicated diagnostics page
    """
    logger.info("Running comprehensive fee account diagnostics...")

    from students.models import Term, Student
    from finances.fee_management.models import FeeCategory

    diagnostics = {
        'summary': {
            'total_issues': 0,
            'critical_issues': 0,
            'warnings': 0,
            'status': 'healthy'
        },
        'terms': {
            'active_terms': [],
            'inactive_terms': [],
            'issues': []
        },
        'fee_categories': {
            'active_categories': [],
            'inactive_categories': [],
            'issues': []
        },
        'students': {
            'active_students_count': 0,
            'inactive_students_count': 0,
            'students_without_accounts': [],
            'students_with_partial_accounts': [],
            'issues': []
        },
        'fee_accounts': {
            'total_accounts': 0,
            'orphaned_accounts': [],
            'duplicate_accounts': [],
            'accounts_with_zero_due': [],
            'issues': []
        },
        'system_health': {
            'database_integrity': True,
            'configuration_valid': True,
            'recommendations': []
        }
    }

    # Analyze Terms
    active_terms = Term.objects.filter(
        is_active=True).select_related('academic_year')
    inactive_terms = Term.objects.filter(is_active=False).select_related(
        'academic_year')[:10]  # Limit for performance

    diagnostics['terms']['active_terms'] = list(active_terms)
    diagnostics['terms']['inactive_terms'] = list(inactive_terms)

    if not active_terms.exists():
        diagnostics['terms']['issues'].append({
            'type': 'critical',
            'message': 'No active term found - fee accounts cannot be created',
            'solution': 'Create and activate a term in Academic Year Management'
        })
        diagnostics['summary']['critical_issues'] += 1
    elif active_terms.count() > 1:
        diagnostics['terms']['issues'].append({
            'type': 'warning',
            'message': f'Multiple active terms found ({active_terms.count()}) - this may cause conflicts',
            'solution': 'Deactivate extra terms, keep only one active',
            'details': [str(term) for term in active_terms]
        })
        diagnostics['summary']['warnings'] += 1

    # Analyze Fee Categories
    active_categories = FeeCategory.objects.filter(is_active=True)
    inactive_categories = FeeCategory.objects.filter(is_active=False)

    diagnostics['fee_categories']['active_categories'] = list(
        active_categories)
    diagnostics['fee_categories']['inactive_categories'] = list(
        inactive_categories)

    if not active_categories.exists():
        diagnostics['fee_categories']['issues'].append({
            'type': 'critical',
            'message': 'No active fee categories found - no accounts will be created',
            'solution': 'Create fee categories and mark them as active'
        })
        diagnostics['summary']['critical_issues'] += 1

    # Analyze Students
    active_students = Student.objects.filter(
        is_active=True).select_related('level')
    inactive_students = Student.objects.filter(
        is_active=False).select_related('level')

    diagnostics['students']['active_students_count'] = active_students.count()
    diagnostics['students']['inactive_students_count'] = inactive_students.count()

    # Find students without fee accounts
    students_without_accounts = []
    students_with_partial_accounts = []

    if active_terms.exists() and active_categories.exists():
        active_term = active_terms.first()

        for student in active_students:
            # Filter categories based on student's education stage (same logic as in student model)
            excluded_categories = (
                ["Primary Tuition Fees", "Primary Food Fees", "PTA"]
                if student.level.education_stage == "Nursery"
                else ["Nursery Tuition Fees", "Nursery Food Fees", "PTA"]
            )

            # Get categories appropriate for this student's education stage
            student_categories = active_categories.exclude(name__in=excluded_categories)
            expected_categories_count = student_categories.count()

            accounts = FeeAccount.objects.filter(
                student=student,
                term=active_term
            ).select_related('category')

            if not accounts.exists():
                students_without_accounts.append({
                    'student': student,
                    'level': student.level.level_name,
                    'student_id': student.student_id or 'Not assigned',
                    'education_stage': student.level.education_stage
                })
            elif accounts.count() < expected_categories_count:
                # Only show missing categories that are appropriate for this student's education stage
                existing_category_ids = accounts.values_list('category_id', flat=True)
                missing_categories = student_categories.exclude(id__in=existing_category_ids)

                if missing_categories.exists():  # Only add if there are actually missing appropriate categories
                    students_with_partial_accounts.append({
                        'student': student,
                        'level': student.level.level_name,
                        'student_id': student.student_id or 'Not assigned',
                        'education_stage': student.level.education_stage,
                        'existing_accounts': accounts.count(),
                        'expected_accounts': expected_categories_count,
                        'missing_categories': list(missing_categories)
                    })

    diagnostics['students']['students_without_accounts'] = students_without_accounts
    diagnostics['students']['students_with_partial_accounts'] = students_with_partial_accounts

    if students_without_accounts:
        diagnostics['students']['issues'].append({
            'type': 'warning',
            'message': f'{len(students_without_accounts)} students have no fee accounts',
            'solution': 'Run "Create Accounts" operation',
            'count': len(students_without_accounts)
        })
        diagnostics['summary']['warnings'] += 1

    if students_with_partial_accounts:
        diagnostics['students']['issues'].append({
            'type': 'warning',
            'message': f'{len(students_with_partial_accounts)} students have incomplete fee accounts',
            'solution': 'Run "Create Accounts" operation to complete missing accounts',
            'count': len(students_with_partial_accounts)
        })
        diagnostics['summary']['warnings'] += 1

    # Analyze Fee Accounts
    total_accounts = FeeAccount.objects.filter(term__is_active=True).count()
    diagnostics['fee_accounts']['total_accounts'] = total_accounts

    # Find orphaned accounts (accounts for inactive students)
    orphaned_accounts = FeeAccount.objects.filter(
        student__is_active=False,
        term__is_active=True
    ).select_related('student', 'category', 'term')

    diagnostics['fee_accounts']['orphaned_accounts'] = [
        {
            'account': account,
            'student_name': account.student.name,
            'student_id': account.student.student_id or 'Not assigned',
            'category': account.category.name,
            'total_due': account.total_due,
            'billing_cycle': account.billing_cycle
        }
        for account in orphaned_accounts
    ]

    if orphaned_accounts.exists():
        diagnostics['fee_accounts']['issues'].append({
            'type': 'warning',
            'message': f'Found {orphaned_accounts.count()} fee accounts for inactive students',
            'solution': 'Run "Re Calculate Accounts" operation to clean up orphaned accounts',
            'count': orphaned_accounts.count()
        })
        diagnostics['summary']['warnings'] += 1

    # Find accounts with zero total_due (potential configuration issues)
    zero_due_accounts = FeeAccount.objects.filter(
        term__is_active=True,
        total_due=0
    ).select_related('student', 'category', 'term')

    diagnostics['fee_accounts']['accounts_with_zero_due'] = [
        {
            'account': account,
            'student_name': account.student.name,
            'student_id': account.student.student_id or 'Not assigned',
            'category': account.category.name,
            'billing_cycle': account.billing_cycle
        }
        for account in zero_due_accounts
    ]

    if zero_due_accounts.exists():
        diagnostics['fee_accounts']['issues'].append({
            'type': 'warning',
            'message': f'Found {zero_due_accounts.count()} fee accounts with zero amount due',
            'solution': 'Review fee category amounts and account configurations',
            'count': zero_due_accounts.count()
        })
        diagnostics['summary']['warnings'] += 1

    # System Health Analysis
    diagnostics['summary']['total_issues'] = (
        diagnostics['summary']['critical_issues'] +
        diagnostics['summary']['warnings']
    )

    if diagnostics['summary']['critical_issues'] > 0:
        diagnostics['summary']['status'] = 'critical'
    elif diagnostics['summary']['warnings'] > 0:
        diagnostics['summary']['status'] = 'warning'
    else:
        diagnostics['summary']['status'] = 'healthy'

    # Generate recommendations
    recommendations = []

    if diagnostics['summary']['critical_issues'] > 0:
        recommendations.append(
            "Address critical issues immediately to restore system functionality")

    if students_without_accounts:
        recommendations.append(
            "Run fee account creation to ensure all active students have proper accounts")

    if orphaned_accounts.exists():
        recommendations.append(
            "Clean up orphaned accounts to maintain database integrity")

    if not recommendations:
        recommendations.append(
            "System is healthy - consider running diagnostics monthly for preventive maintenance")

    diagnostics['system_health']['recommendations'] = recommendations

    # Log comprehensive results
    logger.info(f"Comprehensive diagnostics completed:")
    logger.info(f"  - Status: {diagnostics['summary']['status']}")
    logger.info(
        f"  - Critical Issues: {diagnostics['summary']['critical_issues']}")
    logger.info(f"  - Warnings: {diagnostics['summary']['warnings']}")
    logger.info(f"  - Total Fee Accounts: {total_accounts}")
    logger.info(
        f"  - Active Students: {diagnostics['students']['active_students_count']}")

    return diagnostics
